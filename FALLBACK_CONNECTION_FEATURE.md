# LinkedIn Connection Request Fallback Feature

## Overview
This feature automatically handles failed message attempts by falling back to sending connection requests with custom messages.

## How It Works

### Automatic Fallback
When a direct message fails to send, the system automatically:

1. **Detects Message Failure**: Monitors for failed message attempts (no message button found, send button failed, etc.)
2. **Triggers Fallback**: Automatically switches to connection request mode
3. **Finds Connection Options**: Looks for three-dot menu or direct connect buttons
4. **Fills Invitation Form**: Locates the textarea with id `connect-cta-form__invitation` and fills it with the message
5. **Sends Connection Request**: Clicks the "Send Invitation" button

### Manual Connection Requests
Users can also manually send connection requests only:

1. **Bulk Connection Button**: New "🤝 Send Connection Requests Only" button in the interface
2. **Direct Processing**: Skips message attempts and goes straight to connection requests
3. **Progress Tracking**: Shows progress for each profile processed

## Technical Implementation

### Content Script Changes (`linkedin-content.js`)
- Modified `handleDirectMessage()` to detect failures and trigger fallback
- Added `handleConnectionRequestFallback()` method
- Added helper methods:
  - `findThreeDotMenu()` - Locates three-dot menu buttons
  - `findConnectOption()` - Finds Connect option in dropdown menus
  - `findDirectConnectButton()` - Finds direct Connect buttons
  - `fillInvitationForm()` - Fills the invitation textarea and sends request
- Updated `clickSendButton()` to return success/failure status
- Added new action `sendConnectionRequest` for manual connection requests

### Popup Interface Changes (`popup.js`)
- Added "Send Connection Requests Only" button to bulk processing interface
- Added `startBulkConnectionRequests()` method for manual connection request processing
- Updated status messages to indicate fallback functionality
- Enhanced error handling to mention automatic fallback attempts

## Supported LinkedIn Elements

### Three-Dot Menu Selectors
- `button[aria-label*="More actions"]`
- `button[aria-label*="Open actions overflow menu"]`
- `button[id^="hue-menu-trigger-"]`
- `button._overflow-menu--trigger_1xow7n`
- `button[data-control-name="overflow_menu"]`

### Connect Button Selectors
- `button[aria-label*="Invite"][aria-label*="connect"]`
- `button[aria-label*="Connect"]`
- `button[data-control-name="connect"]`

### Invitation Form Selectors
- `#connect-cta-form__invitation` (primary target as specified by user)
- `textarea[id*="invitation"]`
- `textarea[placeholder*="invitation"]`
- `textarea[maxlength="300"]`
- `.send-invite textarea`

## User Experience

### Automatic Mode
1. User clicks "Send Message" 
2. System attempts to send direct message
3. If message fails → Automatically tries connection request
4. User sees status: "Message sent! (Fallback to connection request if messaging fails)"

### Manual Mode
1. User clicks "🤝 Send Connection Requests Only"
2. System processes each profile with connection requests only
3. Progress bar shows current profile being processed
4. Completion notification shows success rate

## Error Handling
- Graceful fallback when message buttons are not found
- Automatic retry with connection requests when send button fails
- Console logging for debugging failed attempts
- User-friendly status messages indicating fallback attempts

## Message Content
- Uses the same message content for both direct messages and connection requests
- Supports static "Hello dear" message as per user preference
- Maintains message personalization when available
